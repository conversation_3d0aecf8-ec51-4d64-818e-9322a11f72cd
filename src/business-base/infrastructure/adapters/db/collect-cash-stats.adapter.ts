import { Injectable } from '@nestjs/common';
import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { CollectCashStatsPort } from '@business-base/infrastructure/ports/db/collect-cash-stats.port';
import { RecordStatus } from '@common/enums';
import { Prisma } from '@prisma/client';

@Injectable()
export class CollectCashStatsAdapter
  extends PrismaCommonAdapter<CollectCashStatsEntity>
  implements CollectCashStatsPort
{
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'collectCashStats');
  }

  async findByCustomerId(customerId: string): Promise<CollectCashStatsEntity[]> {
    const stats = await this.prisma.client.collectCashStats.findMany({
      where: {
        customerId,
        status: RecordStatus.ACTIVE,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return stats.map(stat => this.mapToEntity(stat));
  }

  async getTotalDealValueByCustomerId(customerId: string): Promise<number> {
    const result = await this.prisma.client.collectCashStats.aggregate({
      where: {
        customerId,
        status: RecordStatus.ACTIVE,
      },
      _sum: {
        dealValue: true,
      },
    });

    return Number(result._sum.dealValue) || 0;
  }

  async getTotalDealValueByPortfolioId(portfolioId: string): Promise<number> {
    const result = await this.prisma.client.collectCashStats.aggregate({
      where: {
        portfolioId,
        status: RecordStatus.ACTIVE,
      },
      _sum: {
        dealValue: true,
      },
    });

    return Number(result._sum.dealValue) || 0;
  }

  async getTotalDealValueByCustomerIdWithDateRange(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    const result = await this.prisma.client.$queryRaw<{ average_ticket: number }[]>(Prisma.sql`
    WITH deal_stats AS (
      SELECT
          COALESCE(SUM(ccs.deal_value), 0) AS total_recovered_value
      FROM business_base.collect_cash_stats ccs
      JOIN business_base.portfolio p ON ccs.portfolio_id = p.id
      JOIN business_base.customer c ON p.customer_id = c.id
      WHERE c.id = ${customerId}::uuid
        AND ccs.status = 'ACTIVE'
        AND c.status = 'ACTIVE'
        AND ccs.created_at BETWEEN ${startDate}::timestamptz AND ${endDate}::timestamptz
    ),
    interaction_stats AS (
      SELECT
          COUNT(CASE WHEN pi.current_status = 'SUCCEED' THEN 1 END) AS successful_negotiations
      FROM business_base.portfolio p
      JOIN business_base.customer c ON p.customer_id = c.id
      JOIN business_base.portfolio_item pi ON p.id = pi.portfolio_id
      WHERE c.id = ${customerId}::uuid
        AND c.status = 'ACTIVE'
        AND pi.created_at BETWEEN ${startDate}::timestamptz AND ${endDate}::timestamptz
    )
    SELECT
        CASE
            WHEN is_stats.successful_negotiations > 0
            THEN (ds.total_recovered_value / is_stats.successful_negotiations)
            ELSE 0
        END AS average_ticket
    FROM deal_stats ds
    CROSS JOIN interaction_stats is_stats;
  `);

    return Number(result[0]?.average_ticket) || 0;
  }

  async getTotalDealValueByPortfolioIdWithDateRange(
    portfolioId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number> {
    const whereClause: any = {
      portfolioId,
      status: RecordStatus.ACTIVE,
    };

    if (startDate || endDate) {
      whereClause.createdAt = {};
      if (startDate) {
        whereClause.createdAt.gte = startDate;
      }
      if (endDate) {
        whereClause.createdAt.lte = endDate;
      }
    }

    const result = await this.prisma.client.collectCashStats.aggregate({
      where: whereClause,
      _sum: {
        dealValue: true,
      },
    });

    return Number(result._sum.dealValue) || 0;
  }

  private mapToEntity(stat: any): CollectCashStatsEntity {
    return new CollectCashStatsEntity(
      stat.customerId,
      stat.portfolioId,
      stat.portfolioItemId,
      stat.workflowId,
      Number(stat.dealValue), // Already in cents from database
      Number(stat.originalDebt), // Already in cents from database
      stat.installments,
      stat.status as RecordStatus,
      stat.createdAt,
      stat.updatedAt,
    );
  }
}
